"""
Production-level Mixpanel Service for Phia Analytics.

This service handles fetching specific analytics data from Mixpanel
for the Phia extension and mobile app analytics dashboard.

Key Metrics:
1. New site activation rate broken down by site
2. Enabled permissions % (first time run)
3. Weekly active Safari extension users
4. Weekly active users since launch
5. Weekly retention metrics

Author: Phia Data Team
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import json

from .mixpanel_client import MixpanelClient

logger = logging.getLogger(__name__)


@dataclass
class SiteActivationMetrics:
    """Site activation metrics data structure."""
    site_domain: str
    phia_shown_count: int
    phia_clicked_count: int
    mobile_extension_shown: int
    mobile_extension_activated: int
    click_rate: float
    mobile_activation_rate: float
    combined_activation_rate: float
    date_range: Dict[str, str]


@dataclass
class PermissionFunnelMetrics:
    """Enabled permissions funnel metrics."""
    enable_phia_views: int
    almost_finished_views: int
    conversion_rate: float
    funnel_completion_rate: float
    date_range: Dict[str, str]


@dataclass
class SafariExtensionMetrics:
    """Safari extension weekly active users."""
    active_users: int
    total_clicks: int
    filtered_clicks: int
    platform_breakdown: Dict[str, int]
    date_range: Dict[str, str]


@dataclass
class WeeklyActiveUsersMetrics:
    """Weekly active users with OS breakdown."""
    total_active_users: int
    heartbeat_events: int
    os_breakdown: Dict[str, int]
    permission_state_breakdown: Dict[str, int]
    date_range: Dict[str, str]


@dataclass
class WeeklyRetentionMetrics:
    """Weekly retention metrics for activated users."""
    cohort_start_date: str
    cohort_end_date: str
    initial_cohort_size: int
    weekly_retention_rates: Dict[str, Dict[str, Any]]
    heartbeat_retention: Dict[str, int]
    phia_clicked_retention: Dict[str, int]
    date_range: Dict[str, str]


class MixpanelServiceError(Exception):
    """Custom exception for Mixpanel service errors."""
    pass


class MixpanelService:
    """
    Production-level Mixpanel service for Phia analytics.
    
    Handles all specific analytics requirements with proper error handling,
    retry logic, and data validation.
    """

    def __init__(
        self,
        project_id: str,
        service_account_username: str,
        service_account_secret: str,
        workspace_id: Optional[str] = None,
        respect_rate_limits: bool = True,
        max_retries: int = 3,
        retry_delay: float = 2.0,
    ):
        """
        Initialize the Mixpanel service.
        
        Args:
            project_id: Mixpanel project ID
            service_account_username: Service account username
            service_account_secret: Service account secret
            workspace_id: Optional workspace ID for Data Views
            respect_rate_limits: Whether to enforce rate limiting
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
        """
        self.project_id = project_id
        self.workspace_id = workspace_id
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Initialize the underlying Mixpanel client
        self.client = MixpanelClient(
            project_id=project_id,
            service_account_username=service_account_username,
            service_account_secret=service_account_secret,
            workspace_id=workspace_id,
            respect_rate_limits=respect_rate_limits,
        )
        
        logger.info(f"Initialized MixpanelService for project: {project_id}")
        if respect_rate_limits:
            logger.info("Rate limiting enabled for production usage")

    def _safe_api_call(self, api_method, *args, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Safely execute an API call with retry logic.
        
        Args:
            api_method: The API method to call
            *args: Positional arguments for the API method
            **kwargs: Keyword arguments for the API method
            
        Returns:
            API response data or None if all retries failed
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"API call attempt {attempt + 1}/{self.max_retries + 1}")
                result = api_method(*args, **kwargs)
                
                if result is not None:
                    return result
                else:
                    logger.warning(f"API call returned None on attempt {attempt + 1}")
                    
            except Exception as e:
                last_exception = e
                logger.warning(f"API call failed on attempt {attempt + 1}: {str(e)}")
                
                if attempt < self.max_retries:
                    sleep_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error(f"All {self.max_retries + 1} attempts failed")
        
        if last_exception:
            raise MixpanelServiceError(f"API call failed after {self.max_retries + 1} attempts: {str(last_exception)}")
        else:
            raise MixpanelServiceError(f"API call returned None after {self.max_retries + 1} attempts")

    def _validate_date_range(self, from_date: str, to_date: str) -> Tuple[str, str]:
        """
        Validate and format date range.
        
        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            
        Returns:
            Tuple of validated date strings
            
        Raises:
            MixpanelServiceError: If dates are invalid
        """
        try:
            start_dt = datetime.strptime(from_date, "%Y-%m-%d")
            end_dt = datetime.strptime(to_date, "%Y-%m-%d")
            
            if start_dt > end_dt:
                raise ValueError("Start date must be before end date")
                
            if end_dt > datetime.now():
                raise ValueError("End date cannot be in the future")
                
            return from_date, to_date
            
        except ValueError as e:
            raise MixpanelServiceError(f"Invalid date range: {str(e)}")

    def get_site_activation_metrics(
        self, 
        from_date: str, 
        to_date: str,
        limit: int = 100
    ) -> List[SiteActivationMetrics]:
        """
        Get new site activation rate broken down by site.
        
        Metrics:
        - phia_shown (total events)
        - phia_clicked (total events) 
        - mobile extension activation rate: resale_insights_activate_shown / resale_insights_activated * 100
        
        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            limit: Maximum number of sites to return
            
        Returns:
            List of SiteActivationMetrics objects
        """
        logger.info(f"Fetching site activation metrics from {from_date} to {to_date}")
        from_date, to_date = self._validate_date_range(from_date, to_date)
        
        # Get phia_shown events by site
        phia_shown_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="phia_shown",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["domain"]',
            event_type="general",
            unit="day",
            limit=limit
        )
        
        # Get phia_clicked events by site
        phia_clicked_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="phia_clicked",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["domain"]',
            event_type="general",
            unit="day",
            limit=limit
        )
        
        # Get mobile extension activation events
        mobile_shown_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="resale_insights_activate_shown",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["domain"]',
            event_type="general",
            unit="day",
            limit=limit
        )
        
        mobile_activated_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="resale_insights_activated",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["domain"]',
            event_type="general",
            unit="day",
            limit=limit
        )
        
        # Process and combine data
        site_metrics = []
        all_sites = set()
        
        # Collect all unique sites
        for data in [phia_shown_data, phia_clicked_data, mobile_shown_data, mobile_activated_data]:
            if data and "data" in data and "values" in data["data"]:
                all_sites.update(data["data"]["values"].keys())
        
        for site in all_sites:
            # Extract totals for each metric
            phia_shown_total = self._sum_site_values(phia_shown_data, site)
            phia_clicked_total = self._sum_site_values(phia_clicked_data, site)
            mobile_shown_total = self._sum_site_values(mobile_shown_data, site)
            mobile_activated_total = self._sum_site_values(mobile_activated_data, site)
            
            # Calculate rates
            click_rate = (phia_clicked_total / phia_shown_total * 100) if phia_shown_total > 0 else 0
            mobile_activation_rate = (mobile_activated_total / mobile_shown_total * 100) if mobile_shown_total > 0 else 0
            
            # Combined activation rate: weighted average of click rate and mobile activation rate
            combined_activation_rate = (click_rate + mobile_activation_rate) / 2
            
            site_metrics.append(SiteActivationMetrics(
                site_domain=site,
                phia_shown_count=phia_shown_total,
                phia_clicked_count=phia_clicked_total,
                mobile_extension_shown=mobile_shown_total,
                mobile_extension_activated=mobile_activated_total,
                click_rate=round(click_rate, 2),
                mobile_activation_rate=round(mobile_activation_rate, 2),
                combined_activation_rate=round(combined_activation_rate, 2),
                date_range={"from": from_date, "to": to_date}
            ))
        
        # Sort by combined activation rate (highest first)
        site_metrics.sort(key=lambda x: x.combined_activation_rate, reverse=True)
        
        logger.info(f"Processed site activation metrics for {len(site_metrics)} sites")
        return site_metrics

    def get_permission_funnel_metrics(
        self, 
        from_date: str, 
        to_date: str
    ) -> PermissionFunnelMetrics:
        """
        Get enabled permissions % (first time run) funnel metrics.
        
        Tracks:
        1. page_view with domain:phia.com, pathname:/mobile/enable-phia
        2. page_view with domain:phia.com, pathname:/mobile/almost-finished
        
        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            
        Returns:
            PermissionFunnelMetrics object
        """
        logger.info(f"Fetching permission funnel metrics from {from_date} to {to_date}")
        from_date, to_date = self._validate_date_range(from_date, to_date)
        
        # Step 1: page_view with pathname:/mobile/enable-phia
        enable_phia_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="page_view",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["pathname"]',
            event_type="unique",
            unit="day",
            where_filter='properties["domain"] == "phia.com" and properties["pathname"] == "/mobile/enable-phia"'
        )
        
        # Step 2: page_view with pathname:/mobile/almost-finished
        almost_finished_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="page_view",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["pathname"]',
            event_type="unique",
            unit="day",
            where_filter='properties["domain"] == "phia.com" and properties["pathname"] == "/mobile/almost-finished"'
        )
        
        # Extract totals
        enable_phia_total = self._sum_segmentation_values(enable_phia_data, "/mobile/enable-phia")
        almost_finished_total = self._sum_segmentation_values(almost_finished_data, "/mobile/almost-finished")
        
        # Calculate conversion rates
        conversion_rate = (almost_finished_total / enable_phia_total * 100) if enable_phia_total > 0 else 0
        funnel_completion_rate = conversion_rate  # Same as conversion rate for 2-step funnel
        
        logger.info(f"Permission funnel: {enable_phia_total} -> {almost_finished_total} ({conversion_rate:.2f}%)")
        
        return PermissionFunnelMetrics(
            enable_phia_views=enable_phia_total,
            almost_finished_views=almost_finished_total,
            conversion_rate=round(conversion_rate, 2),
            funnel_completion_rate=round(funnel_completion_rate, 2),
            date_range={"from": from_date, "to": to_date}
        )

    def get_safari_extension_metrics(
        self, 
        from_date: str, 
        to_date: str
    ) -> SafariExtensionMetrics:
        """
        Get weekly active Safari extension users.
        
        Tracks:
        - Event: phia_clicked
        - Filter: platform is IOS_SAFARI_EXTENSION
        - Filter: url does not contain ?showTutorial=true
        
        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            
        Returns:
            SafariExtensionMetrics object
        """
        logger.info(f"Fetching Safari extension metrics from {from_date} to {to_date}")
        from_date, to_date = self._validate_date_range(from_date, to_date)
        
        # Get phia_clicked events with platform breakdown
        platform_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="phia_clicked",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["Platform"]',
            event_type="unique",
            unit="day",
            where_filter='properties["url"] not like "%?showTutorial=true%"'
        )
        
        # Get total phia_clicked for comparison
        total_clicks_data = self._safe_api_call(
            self.client.get_events_data,
            events=["phia_clicked"],
            from_date=from_date,
            to_date=to_date,
            event_type="general",
            unit="day"
        )
        
        # Get filtered phia_clicked (excluding tutorial)
        filtered_clicks_data = self._safe_api_call(
            self.client.get_events_data,
            events=["phia_clicked"],
            from_date=from_date,
            to_date=to_date,
            event_type="general",
            unit="day"
        )
        
        # Extract Safari extension specific data
        safari_users = self._sum_segmentation_values(platform_data, "IOS_SAFARI_EXTENSION")
        total_clicks = self._sum_events_values(total_clicks_data, "phia_clicked")
        filtered_clicks = self._sum_events_values(filtered_clicks_data, "phia_clicked")
        
        # Build platform breakdown
        platform_breakdown = {}
        if platform_data and "data" in platform_data and "values" in platform_data["data"]:
            for platform, daily_data in platform_data["data"]["values"].items():
                platform_breakdown[platform] = sum(daily_data.values()) if daily_data else 0
        
        logger.info(f"Safari extension users: {safari_users}, Total clicks: {total_clicks}")
        
        return SafariExtensionMetrics(
            active_users=safari_users,
            total_clicks=total_clicks,
            filtered_clicks=filtered_clicks,
            platform_breakdown=platform_breakdown,
            date_range={"from": from_date, "to": to_date}
        )

    def get_weekly_active_users_metrics(
        self, 
        from_date: str, 
        to_date: str
    ) -> WeeklyActiveUsersMetrics:
        """
        Get weekly active users since launch.
        
        Tracks:
        - Event: heartbeat (distinct count of phia_id)
        - Filter: permission_state is All
        - Breakdown: Operating System (property)
        
        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            
        Returns:
            WeeklyActiveUsersMetrics object
        """
        logger.info(f"Fetching weekly active users metrics from {from_date} to {to_date}")
        from_date, to_date = self._validate_date_range(from_date, to_date)
        
        # Get heartbeat events with OS breakdown
        os_breakdown_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="heartbeat",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["$os"]',
            event_type="unique",
            unit="week",
            where_filter='properties["permission_state"] == "All"'
        )
        
        # Get permission state breakdown
        permission_breakdown_data = self._safe_api_call(
            self.client.get_segmentation_data,
            event="heartbeat",
            from_date=from_date,
            to_date=to_date,
            segment_property='properties["permission_state"]',
            event_type="unique",
            unit="week"
        )
        
        # Get total heartbeat events
        total_heartbeat_data = self._safe_api_call(
            self.client.get_events_data,
            events=["heartbeat"],
            from_date=from_date,
            to_date=to_date,
            event_type="unique",
            unit="week"
        )
        
        # Extract data
        total_active_users = self._sum_events_values(total_heartbeat_data, "heartbeat")
        heartbeat_events = self._sum_events_values(total_heartbeat_data, "heartbeat")
        
        # Build OS breakdown
        os_breakdown = {}
        if os_breakdown_data and "data" in os_breakdown_data and "values" in os_breakdown_data["data"]:
            for os, weekly_data in os_breakdown_data["data"]["values"].items():
                os_breakdown[os] = sum(weekly_data.values()) if weekly_data else 0
        
        # Build permission state breakdown
        permission_breakdown = {}
        if permission_breakdown_data and "data" in permission_breakdown_data and "values" in permission_breakdown_data["data"]:
            for state, weekly_data in permission_breakdown_data["data"]["values"].items():
                permission_breakdown[state] = sum(weekly_data.values()) if weekly_data else 0
        
        logger.info(f"Weekly active users: {total_active_users}, Heartbeat events: {heartbeat_events}")
        
        return WeeklyActiveUsersMetrics(
            total_active_users=total_active_users,
            heartbeat_events=heartbeat_events,
            os_breakdown=os_breakdown,
            permission_state_breakdown=permission_breakdown,
            date_range={"from": from_date, "to": to_date}
        )

    def get_weekly_retention_metrics(
        self, 
        cohort_start_date: str,
        cohort_end_date: str,
        analysis_weeks: int = 8
    ) -> WeeklyRetentionMetrics:
        """
        Get weekly retention metrics for activated users.
        
        Tracks:
        - heartbeat: total events
        - phia_clicked: total events
        - Filter: users who activated permission
        - Filter: first seen between cohort dates
        - Retention criteria: each calendar week
        
        Args:
            cohort_start_date: Cohort start date (YYYY-MM-DD)
            cohort_end_date: Cohort end date (YYYY-MM-DD)
            analysis_weeks: Number of weeks to analyze retention
            
        Returns:
            WeeklyRetentionMetrics object
        """
        logger.info(f"Fetching weekly retention metrics for cohort {cohort_start_date} to {cohort_end_date}")
        cohort_start_date, cohort_end_date = self._validate_date_range(cohort_start_date, cohort_end_date)
        
        # Get users who activated permissions in the cohort period
        cohort_users_data = self._safe_api_call(
            self.client.get_user_profiles,
            where_filter=f'properties["$first_seen"] >= "{cohort_start_date}" and properties["$first_seen"] <= "{cohort_end_date}" and properties["permission_activated"] == true',
            output_properties=["$first_seen", "$last_seen", "phia_id", "permission_activated"]
        )
        
        if not cohort_users_data or not cohort_users_data.get("results"):
            logger.warning(f"No activated users found in cohort period {cohort_start_date} to {cohort_end_date}")
            return WeeklyRetentionMetrics(
                cohort_start_date=cohort_start_date,
                cohort_end_date=cohort_end_date,
                initial_cohort_size=0,
                weekly_retention_rates={},
                heartbeat_retention={},
                phia_clicked_retention={},
                date_range={"from": cohort_start_date, "to": cohort_end_date}
            )
        
        cohort_users = cohort_users_data["results"]
        initial_cohort_size = len(cohort_users)
        user_ids = [user["$distinct_id"] for user in cohort_users]
        
        logger.info(f"Analyzing retention for {initial_cohort_size} users in cohort")
        
        # Calculate retention for each week
        weekly_retention_rates = {}
        heartbeat_retention = {}
        phia_clicked_retention = {}
        
        start_dt = datetime.strptime(cohort_start_date, "%Y-%m-%d")
        
        for week in range(analysis_weeks):
            week_start = start_dt + timedelta(weeks=week)
            week_end = week_start + timedelta(days=6)
            week_start_str = week_start.strftime("%Y-%m-%d")
            week_end_str = week_end.strftime("%Y-%m-%d")
            
            # Get heartbeat events for this week
            heartbeat_data = self._safe_api_call(
                self.client.get_events_data,
                events=["heartbeat"],
                from_date=week_start_str,
                to_date=week_end_str,
                event_type="unique",
                unit="day"
            )
            
            # Get phia_clicked events for this week
            phia_clicked_data = self._safe_api_call(
                self.client.get_events_data,
                events=["phia_clicked"],
                from_date=week_start_str,
                to_date=week_end_str,
                event_type="unique",
                unit="day"
            )
            
            # Extract retention counts
            heartbeat_count = self._sum_events_values(heartbeat_data, "heartbeat")
            phia_clicked_count = self._sum_events_values(phia_clicked_data, "phia_clicked")
            
            # Calculate retention rates
            heartbeat_retention_rate = (heartbeat_count / initial_cohort_size * 100) if initial_cohort_size > 0 else 0
            phia_clicked_retention_rate = (phia_clicked_count / initial_cohort_size * 100) if initial_cohort_size > 0 else 0
            
            week_key = f"week_{week}"
            weekly_retention_rates[week_key] = {
                "week_start": week_start_str,
                "week_end": week_end_str,
                "heartbeat_retention_rate": round(heartbeat_retention_rate, 2),
                "phia_clicked_retention_rate": round(phia_clicked_retention_rate, 2),
                "heartbeat_users": heartbeat_count,
                "phia_clicked_users": phia_clicked_count,
                "cohort_size": initial_cohort_size
            }
            
            heartbeat_retention[week_key] = heartbeat_count
            phia_clicked_retention[week_key] = phia_clicked_count
        
        logger.info(f"Completed retention analysis for {analysis_weeks} weeks")
        
        return WeeklyRetentionMetrics(
            cohort_start_date=cohort_start_date,
            cohort_end_date=cohort_end_date,
            initial_cohort_size=initial_cohort_size,
            weekly_retention_rates=weekly_retention_rates,
            heartbeat_retention=heartbeat_retention,
            phia_clicked_retention=phia_clicked_retention,
            date_range={"from": cohort_start_date, "to": cohort_end_date}
        )

    def get_comprehensive_analytics(
        self,
        from_date: str,
        to_date: str,
        cohort_start_date: Optional[str] = None,
        cohort_end_date: Optional[str] = None,
        analysis_weeks: int = 8
    ) -> Dict[str, Any]:
        """
        Get all analytics data in a single comprehensive call.
        
        Args:
            from_date: Start date for general metrics (YYYY-MM-DD)
            to_date: End date for general metrics (YYYY-MM-DD)
            cohort_start_date: Start date for retention cohort (YYYY-MM-DD)
            cohort_end_date: End date for retention cohort (YYYY-MM-DD)
            analysis_weeks: Number of weeks to analyze retention
            
        Returns:
            Dictionary containing all analytics data
        """
        logger.info(f"Fetching comprehensive analytics from {from_date} to {to_date}")
        
        # Use default cohort dates if not provided
        if not cohort_start_date:
            cohort_start_date = "2024-06-19"  # Default cohort start
        if not cohort_end_date:
            cohort_end_date = "2024-06-25"   # Default cohort end
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "date_range": {"from": from_date, "to": to_date},
            "cohort_range": {"from": cohort_start_date, "to": cohort_end_date},
            "success": True,
            "data": {}
        }
        
        try:
            # 1. Site activation metrics
            logger.info("Fetching site activation metrics...")
            site_activation = self.get_site_activation_metrics(from_date, to_date)
            results["data"]["site_activation_metrics"] = [asdict(metric) for metric in site_activation]
            
            # 2. Permission funnel metrics
            logger.info("Fetching permission funnel metrics...")
            permission_funnel = self.get_permission_funnel_metrics(from_date, to_date)
            results["data"]["permission_funnel_metrics"] = asdict(permission_funnel)
            
            # 3. Safari extension metrics
            logger.info("Fetching Safari extension metrics...")
            safari_metrics = self.get_safari_extension_metrics(from_date, to_date)
            results["data"]["safari_extension_metrics"] = asdict(safari_metrics)
            
            # 4. Weekly active users metrics
            logger.info("Fetching weekly active users metrics...")
            weekly_active_users = self.get_weekly_active_users_metrics(from_date, to_date)
            results["data"]["weekly_active_users_metrics"] = asdict(weekly_active_users)
            
            # 5. Weekly retention metrics
            logger.info("Fetching weekly retention metrics...")
            retention_metrics = self.get_weekly_retention_metrics(
                cohort_start_date, cohort_end_date, analysis_weeks
            )
            results["data"]["weekly_retention_metrics"] = asdict(retention_metrics)
            
            logger.info("Successfully fetched all comprehensive analytics data")
            
        except Exception as e:
            logger.error(f"Error fetching comprehensive analytics: {str(e)}")
            results["success"] = False
            results["error"] = str(e)
        
        return results

    def _sum_site_values(self, data: Optional[Dict], site: str) -> int:
        """Sum values for a specific site across all days."""
        if not data or "data" not in data or "values" not in data["data"]:
            return 0
        
        site_data = data["data"]["values"].get(site, {})
        return sum(site_data.values()) if site_data else 0

    def _sum_segmentation_values(self, data: Optional[Dict], segment: str) -> int:
        """Sum values for a specific segment across all days."""
        if not data or "data" not in data or "values" not in data["data"]:
            return 0
        
        segment_data = data["data"]["values"].get(segment, {})
        return sum(segment_data.values()) if segment_data else 0

    def _sum_events_values(self, data: Optional[Dict], event: str) -> int:
        """Sum values for a specific event across all days."""
        if not data or "data" not in data or "values" not in data["data"]:
            return 0
        
        event_data = data["data"]["values"].get(event, {})
        return sum(event_data.values()) if event_data else 0

    def test_connection(self) -> bool:
        """
        Test the Mixpanel service connection.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            logger.info("Testing Mixpanel service connection...")
            result = self.client.test_connection()
            
            if result:
                logger.info("Mixpanel service connection test successful")
                return True
            else:
                logger.error("Mixpanel service connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"Mixpanel service connection test failed: {str(e)}")
            return False

    def get_service_health(self) -> Dict[str, Any]:
        """
        Get service health status and configuration.
        
        Returns:
            Dictionary containing service health information
        """
        return {
            "service_name": "MixpanelService",
            "project_id": self.project_id,
            "workspace_id": self.workspace_id,
            "rate_limiting_enabled": self.client.respect_rate_limits,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "connection_healthy": self.test_connection(),
            "api_calls_made": getattr(self.client, 'request_count', 0),
            "timestamp": datetime.now().isoformat()
        }