"""
Airflow task functions for Mixpanel data pipeline.

This module contains the main task functions that will be called by the DAG:
- fetch_mixpanel_analytics_task: Fetch all analytics data from Mixpanel API using new MixpanelService
- transform_mixpanel_data_task: Transform raw data to normalized schemas
- prepare_supabase_data_task: Prepare data for Supabase upload

Updated to use the new MixpanelService with 5 key metrics:
1. Site activation metrics (phia_shown/phia_clicked by domain)
2. Permission funnel metrics (enable-phia → almost-finished conversion)
3. Safari extension metrics (IOS_SAFARI_EXTENSION platform filter)
4. Weekly active users (heartbeat events with OS breakdown)
5. Weekly retention metrics (cohort analysis)
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List
from dataclasses import asdict

from .mixpanel_service import MixpanelService
from .mixpanel_transform import validate_transformed_data

logger = logging.getLogger(__name__)


def fetch_mixpanel_analytics_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to fetch comprehensive analytics data from Mixpanel API using new MixpanelService.

    This task uses the new MixpanelService to fetch the 5 key metrics:
    1. Site activation metrics (phia_shown/phia_clicked by domain)
    2. Permission funnel metrics (enable-phia → almost-finished conversion)
    3. Safari extension metrics (IOS_SAFARI_EXTENSION platform filter)
    4. Weekly active users (heartbeat events with OS breakdown)
    5. Weekly retention metrics (cohort analysis)

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with all analytics data from MixpanelService
    """
    logger.info("Starting Mixpanel analytics fetch task with new MixpanelService")

    # Get execution date and calculate date ranges
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    # Calculate date range for analytics (last 30 days)
    from_date = (target_date - timedelta(days=30)).strftime("%Y-%m-%d")
    to_date = date_str

    logger.info(f"Fetching Mixpanel analytics from {from_date} to {to_date}")

    try:
        # Initialize MixpanelService with credentials from environment
        service = MixpanelService(
            project_id=os.getenv("MIXPANEL_PROJECT_ID"),
            service_account_username=os.getenv("MIXPANEL_SERVICE_USERNAME"),
            service_account_secret=os.getenv("MIXPANEL_SERVICE_SECRET"),
            workspace_id=os.getenv("MIXPANEL_WORKSPACE_ID"),
            respect_rate_limits=True,  # Always respect rate limits in production
        )

        # Test connection first
        if not service.test_connection():
            raise Exception("Mixpanel service connection test failed")

        # Fetch comprehensive analytics using the new service
        analytics_data = service.get_comprehensive_analytics(
            from_date=from_date,
            to_date=to_date,
            cohort_start_date="2024-06-19",  # Default cohort for retention
            cohort_end_date="2024-06-25",
            analysis_weeks=8,
        )

        if analytics_data and analytics_data.get("success"):
            logger.info("Successfully fetched comprehensive analytics data")

            # Log summary of fetched data
            data = analytics_data.get("data", {})
            logger.info(
                f"Site activation metrics: {len(data.get('site_activation_metrics', []))} sites"
            )
            logger.info(
                f"Permission funnel conversion: {data.get('permission_funnel_metrics', {}).get('conversion_rate', 0)}%"
            )
            logger.info(
                f"Safari extension users: {data.get('safari_extension_metrics', {}).get('active_users', 0)}"
            )
            logger.info(
                f"Weekly active users: {data.get('weekly_active_users_metrics', {}).get('total_active_users', 0)}"
            )
            logger.info(
                f"Retention cohort size: {data.get('weekly_retention_metrics', {}).get('initial_cohort_size', 0)}"
            )

            return analytics_data
        else:
            error_msg = (
                analytics_data.get("error", "Unknown error")
                if analytics_data
                else "No data returned"
            )
            logger.error(f"Failed to fetch analytics data: {error_msg}")
            raise Exception(f"Mixpanel fetch failed: {error_msg}")

    except Exception as e:
        logger.error(f"Error in fetch task: {str(e)}")
        raise


def transform_mixpanel_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to transform MixpanelService data to normalized Supabase schemas.

    This task:
    - Gets analytics data from MixpanelService via XCom
    - Transforms the 5 key metrics to Supabase table schemas
    - Validates data quality
    - Returns transformed data for each table

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with transformed data for each Supabase table
    """
    logger.info("Starting Mixpanel data transformation task for new service data")

    # Get analytics data from previous task
    ti = kwargs["ti"]
    analytics_data = ti.xcom_pull(task_ids="fetch_mixpanel_analytics")

    if not analytics_data or not analytics_data.get("success"):
        raise Exception("No valid analytics data received from fetch task")

    # Get target date
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    logger.info(f"Transforming MixpanelService data for {date_str}")

    raw_data = analytics_data.get("data", {})
    transformed_data = {}
    validation_results = {}

    try:
        # 1. Transform site activation metrics
        if raw_data.get("site_activation_metrics"):
            logger.info("Transforming site activation metrics...")
            site_metrics = []
            for metric in raw_data["site_activation_metrics"]:
                site_record = {
                    "date": date_str,
                    "site_domain": metric["site_domain"],
                    "total_users": metric["phia_shown_count"],
                    "activated_users": metric["phia_clicked_count"],
                    "activation_rate": metric["combined_activation_rate"],
                    "created_at": datetime.now().isoformat(),
                }
                site_metrics.append(site_record)

            transformed_data["site_activation"] = site_metrics
            validation_results["site_activation"] = validate_transformed_data(
                site_metrics, "site_activation"
            )

        # 2. Transform permission funnel metrics
        if raw_data.get("permission_funnel_metrics"):
            logger.info("Transforming permission funnel metrics...")
            funnel_data = raw_data["permission_funnel_metrics"]
            funnel_records = [
                {
                    "date": date_str,
                    "funnel_step": "enable_phia",
                    "step_order": 1,
                    "users_count": funnel_data["enable_phia_views"],
                    "conversion_rate": 100.0,  # First step is 100%
                    "created_at": datetime.now().isoformat(),
                },
                {
                    "date": date_str,
                    "funnel_step": "almost_finished",
                    "step_order": 2,
                    "users_count": funnel_data["almost_finished_views"],
                    "conversion_rate": funnel_data["conversion_rate"],
                    "created_at": datetime.now().isoformat(),
                },
            ]

            transformed_data["onboarding_funnel"] = funnel_records
            validation_results["onboarding_funnel"] = validate_transformed_data(
                funnel_records, "onboarding_funnel"
            )

        # 3. Transform Safari extension metrics
        if raw_data.get("safari_extension_metrics"):
            logger.info("Transforming Safari extension metrics...")
            safari_data = raw_data["safari_extension_metrics"]
            safari_records = [
                {
                    "date": date_str,
                    "metric_name": "weekly_active_safari_users",
                    "event_name": "phia_clicked",
                    "total_events": safari_data["total_clicks"],
                    "unique_users": safari_data["active_users"],
                    "platform": "IOS_SAFARI_EXTENSION",
                    "created_at": datetime.now().isoformat(),
                }
            ]

            transformed_data["safari_extension_metrics"] = safari_records
            validation_results["safari_extension_metrics"] = validate_transformed_data(
                safari_records, "safari_extension_metrics"
            )

        # 4. Transform weekly active users metrics
        if raw_data.get("weekly_active_users_metrics"):
            logger.info("Transforming weekly active users metrics...")
            weekly_data = raw_data["weekly_active_users_metrics"]
            daily_metrics = [
                {
                    "date": date_str,
                    "event_name": "heartbeat",
                    "total_events": weekly_data["heartbeat_events"],
                    "unique_users": weekly_data["total_active_users"],
                    "metric_type": "weekly_active_users",
                    "created_at": datetime.now().isoformat(),
                }
            ]

            transformed_data["daily_metrics"] = daily_metrics
            validation_results["daily_metrics"] = validate_transformed_data(
                daily_metrics, "daily_metrics"
            )

        # 5. Transform weekly retention metrics
        if raw_data.get("weekly_retention_metrics"):
            logger.info("Transforming weekly retention metrics...")
            retention_data = raw_data["weekly_retention_metrics"]
            cohort_records = []

            for week_key, week_data in retention_data["weekly_retention_rates"].items():
                cohort_record = {
                    "cohort_start_date": retention_data["cohort_start_date"],
                    "cohort_id": 1,  # Default cohort ID
                    "cohort_name": f"Cohort {retention_data['cohort_start_date']}",
                    "cohort_size": retention_data["initial_cohort_size"],
                    "week_number": int(week_key.split("_")[1])
                    + 1,  # week_0 -> 1, week_1 -> 2, etc.
                    "active_users": week_data["heartbeat_users"],
                    "retention_rate": week_data["heartbeat_retention_rate"],
                    "week_start_date": week_data["week_start"],
                    "week_end_date": week_data["week_end"],
                    "created_at": datetime.now().isoformat(),
                }
                cohort_records.append(cohort_record)

            transformed_data["cohort_retention"] = cohort_records
            validation_results["cohort_retention"] = validate_transformed_data(
                cohort_records, "cohort_retention"
            )

        # Calculate overall statistics
        total_records = sum(len(data) for data in transformed_data.values())
        total_valid = sum(
            result.get("valid_records", 0) for result in validation_results.values()
        )

        results = {
            "success": True,
            "target_date": date_str,
            "transformed_data": transformed_data,
            "validation_results": validation_results,
            "statistics": {
                "total_records": total_records,
                "total_valid_records": total_valid,
                "tables_created": len(transformed_data),
                "processing_time": datetime.now().isoformat(),
            },
        }

        logger.info(
            f"Transformation complete: {total_records} records across {len(transformed_data)} tables"
        )
        return results

    except Exception as e:
        logger.error(f"Error in transform task: {str(e)}")
        raise


def prepare_supabase_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to prepare transformed data for Supabase upload.

    This task:
    - Gets transformed data from previous task
    - Formats data for Supabase batch upload
    - Performs final validation
    - Returns data ready for Supabase upload

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with data prepared for Supabase upload
    """
    logger.info("Starting Supabase data preparation task")

    # Get transformed data from previous task
    ti = kwargs["ti"]
    transform_results = ti.xcom_pull(task_ids="transform_mixpanel_data")

    if not transform_results or not transform_results.get("success"):
        raise Exception("No valid transformed data received from transform task")

    transformed_data = transform_results.get("transformed_data", {})
    target_date = transform_results.get("target_date")

    logger.info(f"Preparing {len(transformed_data)} tables for Supabase upload")

    # Prepare data for each table
    supabase_ready_data = {}
    upload_statistics = {}

    for table_name, records in transformed_data.items():
        if records:
            # Filter out any records with missing required fields
            valid_records = []
            for record in records:
                # Remove any None values that would cause Supabase issues
                cleaned_record = {k: v for k, v in record.items() if v is not None}
                if cleaned_record:
                    valid_records.append(cleaned_record)

            supabase_ready_data[table_name] = valid_records
            upload_statistics[table_name] = {
                "total_records": len(records),
                "valid_records": len(valid_records),
                "ready_for_upload": len(valid_records) > 0,
            }

            logger.info(
                f"{table_name}: {len(valid_records)}/{len(records)} records ready for upload"
            )

    results = {
        "success": True,
        "target_date": target_date,
        "supabase_data": supabase_ready_data,
        "upload_statistics": upload_statistics,
        "total_tables": len(supabase_ready_data),
        "total_records": sum(len(data) for data in supabase_ready_data.values()),
        "prepared_at": datetime.now().isoformat(),
    }

    logger.info(
        f"Supabase preparation complete: {results['total_records']} records across {results['total_tables']} tables"
    )
    return results
