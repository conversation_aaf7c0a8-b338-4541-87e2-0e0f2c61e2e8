#!/usr/bin/env python3
"""
Full DAG Local Test Script - Updated for New MixpanelService

This script simulates running the entire Mixpanel DAG locally to test:
1. Data fetching from Mixpanel API using new MixpanelService
2. Data transformation to Supabase schemas for 5 key metrics
3. Data preparation and validation
4. Data upload to Supabase
5. Verification of data in Supabase tables

KEY METRICS TESTED (from MixpanelService):
1. Site activation metrics (phia_shown/phia_clicked by domain)
2. Permission funnel metrics (enable-phia → almost-finished conversion)
3. Safari extension metrics (IOS_SAFARI_EXTENSION platform filter)
4. Weekly active users (heartbeat events with OS breakdown)
5. Weekly retention metrics (cohort analysis)

Run this before deploying to Airflow to ensure everything works end-to-end.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags", "dependencies"))

from mixpanel.mixpanel_tasks import (
    transform_mixpanel_data_task,
    prepare_supabase_data_task,
)


def create_mock_airflow_context(execution_date=None):
    """Create a mock Airflow context for testing."""
    if execution_date is None:
        execution_date = datetime.now()

    class MockTaskInstance:
        def __init__(self):
            self.xcom_data = {}

        def xcom_pull(self, task_ids):
            return self.xcom_data.get(task_ids)

        def xcom_push(self, key, value):
            self.xcom_data[key] = value

    ti = MockTaskInstance()

    return {
        "execution_date": execution_date,
        "ti": ti,
        "ds": execution_date.strftime("%Y-%m-%d"),
        "ds_nodash": execution_date.strftime("%Y%m%d"),
        "task_instance": ti,
    }


def test_supabase_connection():
    """Test connection to Supabase and verify tables exist."""
    print("🔍 Testing Supabase connection...")

    try:
        # Import Supabase client
        from supabase import create_client, Client

        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not url or not key:
            print("❌ Missing Supabase credentials:")
            print("   - SUPABASE_URL")
            print("   - SUPABASE_SERVICE_ROLE_KEY")
            return None

        supabase: Client = create_client(url, key)

        # Test connection by checking if tables exist for MixpanelService data
        tables_to_check = [
            "site_activation",  # Site activation metrics by domain
            "onboarding_funnel",  # Permission funnel conversion metrics
            "safari_extension_metrics",  # Safari extension specific metrics
            "daily_metrics",  # Weekly active users data
            "cohort_retention",  # Weekly retention analysis
        ]

        existing_tables = []
        for table in tables_to_check:
            try:
                # Try to query the table (limit 0 to just check existence)
                result = supabase.table(table).select("*").limit(0).execute()
                existing_tables.append(table)
                print(f"   ✅ Table '{table}' exists")
            except Exception as e:
                print(
                    f"   ❌ Table '{table}' missing or inaccessible: {str(e)[:100]}..."
                )

        if len(existing_tables) == len(tables_to_check):
            print("✅ All Mixpanel tables exist in Supabase")
            return supabase
        else:
            print(f"⚠️ Only {len(existing_tables)}/{len(tables_to_check)} tables exist")
            print("💡 Run the migrations:")
            print("   - supabase/migrations/001_create_mixpanel_tables.sql")
            print("   - supabase/migrations/002_add_new_dashboard_components.sql")
            return supabase

    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return None


def upload_to_supabase(supabase_data, supabase_client):
    """Upload prepared data to Supabase tables."""
    print("\n📤 Uploading data to Supabase...")

    upload_results = {}

    for table_name, records in supabase_data.items():
        if not records:
            print(f"   ⚠️ No data for table '{table_name}'")
            continue

        try:
            print(f"   📊 Uploading {len(records)} records to '{table_name}'...")

            # Use upsert to handle duplicates
            result = supabase_client.table(table_name).upsert(records).execute()

            if result.data:
                upload_results[table_name] = {
                    "success": True,
                    "records_uploaded": len(result.data),
                    "total_records": len(records),
                }
                print(
                    f"   ✅ {table_name}: {len(result.data)} records uploaded successfully"
                )
            else:
                upload_results[table_name] = {
                    "success": False,
                    "error": "No data returned from upsert",
                    "total_records": len(records),
                }
                print(f"   ❌ {table_name}: Upload failed - no data returned")

        except Exception as e:
            upload_results[table_name] = {
                "success": False,
                "error": str(e),
                "total_records": len(records),
            }
            print(f"   ❌ {table_name}: Upload failed - {str(e)[:100]}...")

    return upload_results


def verify_data_in_supabase(supabase_client, target_date):
    """Verify that data was correctly inserted into Supabase."""
    print(f"\n🔍 Verifying data in Supabase for {target_date}...")

    verification_results = {}

    tables_to_verify = {
        "site_activation": "date",  # Site activation metrics by domain
        "onboarding_funnel": "date",  # Permission funnel conversion metrics
        "safari_extension_metrics": "date",  # Safari extension specific metrics
        "daily_metrics": "date",  # Weekly active users data
        "cohort_retention": "cohort_start_date",  # Weekly retention analysis
    }

    for table_name, date_column in tables_to_verify.items():
        try:
            if date_column:
                # Query with date filter
                result = (
                    supabase_client.table(table_name)
                    .select("*")
                    .eq(date_column, target_date)
                    .execute()
                )
            else:
                # Query recent records (last 10)
                result = (
                    supabase_client.table(table_name)
                    .select("*")
                    .order("updated_at", desc=True)
                    .limit(10)
                    .execute()
                )

            record_count = len(result.data) if result.data else 0
            verification_results[table_name] = {
                "record_count": record_count,
                "sample_data": (
                    result.data[:2] if result.data else []
                ),  # First 2 records as sample
            }

            print(f"   📊 {table_name}: {record_count} records found")

            # Show sample data structure
            if result.data:
                sample = result.data[0]
                key_fields = list(sample.keys())[:5]  # First 5 fields
                print(f"      🔍 Sample fields: {key_fields}")

        except Exception as e:
            verification_results[table_name] = {"error": str(e), "record_count": 0}
            print(f"   ❌ {table_name}: Verification failed - {str(e)[:100]}...")

    return verification_results


def run_full_dag_test():
    """Run the complete DAG test locally."""
    print("🚀 Starting Full DAG Local Test")
    print("=" * 70)

    # Setup
    execution_date = datetime.now()
    target_date = (execution_date - timedelta(days=1)).strftime("%Y-%m-%d")
    context = create_mock_airflow_context(execution_date)

    print(f"📅 Target date: {target_date}")
    print(f"🕐 Execution time: {execution_date.strftime('%Y-%m-%d %H:%M:%S')}")

    # Test Supabase connection first
    supabase_client = test_supabase_connection()
    if not supabase_client:
        print("❌ Cannot proceed without Supabase connection")
        return

    try:
        # Step 1: Fetch Mixpanel Analytics Data (using working pattern)
        print(f"\n1️⃣ STEP 1: Fetching Mixpanel Analytics Data")
        print("-" * 50)

        start_time = time.time()

        # Use the same pattern as your working quick_mixpanel_test.py
        print("   🔗 Initializing MixpanelService...")
        from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

        service = MixpanelService(
            project_id=os.getenv("MIXPANEL_PROJECT_ID"),
            service_account_username=os.getenv("MIXPANEL_SERVICE_USERNAME"),
            service_account_secret=os.getenv("MIXPANEL_SERVICE_SECRET"),
            workspace_id=os.getenv("MIXPANEL_WORKSPACE_ID"),
            respect_rate_limits=False,  # Disable for testing like working test
        )

        # Test connection with simple approach like working test
        print("   🔗 Testing connection...")
        try:
            test_events = service.client.get_events_data(
                events=["phia_clicked"],
                from_date=target_date,
                to_date=target_date,
                event_type="general",
                unit="day",
            )
            if not test_events or "data" not in test_events:
                raise Exception("Connection test failed")
            print("   ✅ Connection successful!")
        except Exception as e:
            raise Exception(f"Connection test failed: {str(e)}")

        # Fetch comprehensive analytics
        print("   📊 Fetching comprehensive analytics...")
        analytics_data = service.get_comprehensive_analytics(
            from_date=(
                datetime.strptime(target_date, "%Y-%m-%d") - timedelta(days=7)
            ).strftime("%Y-%m-%d"),
            to_date=target_date,
            cohort_start_date="2024-06-19",
            cohort_end_date="2024-06-25",
            analysis_weeks=8,
        )

        fetch_time = time.time() - start_time

        # Store in mock XCom
        context["ti"].xcom_push("fetch_mixpanel_analytics", analytics_data)

        print(f"✅ Fetch completed in {fetch_time:.1f} seconds")
        print(f"📊 API calls used: {analytics_data.get('api_calls_used', 'unknown')}")
        print(f"📈 Data types: {list(analytics_data.get('data', {}).keys())}")

        # Show MixpanelService metrics data
        data = analytics_data.get("data", {})
        if "site_activation_metrics" in data:
            site_metrics = data["site_activation_metrics"]
            if site_metrics:
                total_sites = len(site_metrics)
                avg_rate = (
                    sum(m.get("combined_activation_rate", 0) for m in site_metrics)
                    / total_sites
                    if total_sites > 0
                    else 0
                )
                print(
                    f"   📈 Site Activation: {total_sites} sites, avg rate {avg_rate:.1f}%"
                )

        if "permission_funnel_metrics" in data:
            funnel_data = data["permission_funnel_metrics"]
            if funnel_data:
                conversion = funnel_data.get("conversion_rate", 0)
                print(f"   🎯 Permission Funnel Conversion: {conversion}%")

        if "safari_extension_metrics" in data:
            safari_data = data["safari_extension_metrics"]
            if safari_data:
                users = safari_data.get("active_users", 0)
                print(f"   🦎 Safari Extension Users: {users:,}")

        if "weekly_active_users_metrics" in data:
            weekly_data = data["weekly_active_users_metrics"]
            if weekly_data:
                users = weekly_data.get("total_active_users", 0)
                print(f"   👥 Weekly Active Users: {users:,}")

        if "weekly_retention_metrics" in data:
            retention_data = data["weekly_retention_metrics"]
            if retention_data:
                cohort_size = retention_data.get("initial_cohort_size", 0)
                print(f"   📊 Retention Cohort Size: {cohort_size:,}")

        # Step 2: Transform Data
        print(f"\n2️⃣ STEP 2: Transforming Data to Supabase Schemas")
        print("-" * 50)

        start_time = time.time()
        transform_results = transform_mixpanel_data_task(**context)
        transform_time = time.time() - start_time

        # Store in mock XCom
        context["ti"].xcom_push("transform_mixpanel_data", transform_results)

        print(f"✅ Transform completed in {transform_time:.1f} seconds")
        print(
            f"📊 Tables created: {transform_results.get('statistics', {}).get('tables_created', 0)}"
        )
        print(
            f"📈 Total records: {transform_results.get('statistics', {}).get('total_records', 0)}"
        )

        # Show validation results
        validation_results = transform_results.get("validation_results", {})
        for table, validation in validation_results.items():
            success_rate = validation.get("success_rate", 0)
            print(f"   📋 {table}: {success_rate}% validation success")

        # Step 3: Prepare for Supabase
        print(f"\n3️⃣ STEP 3: Preparing Data for Supabase Upload")
        print("-" * 50)

        start_time = time.time()
        supabase_prep = prepare_supabase_data_task(**context)
        prep_time = time.time() - start_time

        print(f"✅ Preparation completed in {prep_time:.1f} seconds")
        print(f"📊 Tables ready: {supabase_prep.get('total_tables', 0)}")
        print(f"📈 Records ready: {supabase_prep.get('total_records', 0)}")

        # Show upload statistics
        upload_stats = supabase_prep.get("upload_statistics", {})
        for table, stats in upload_stats.items():
            ready = stats.get("valid_records", 0)
            total = stats.get("total_records", 0)
            print(f"   📋 {table}: {ready}/{total} records ready")

        # Step 4: Upload to Supabase
        print(f"\n4️⃣ STEP 4: Uploading Data to Supabase")
        print("-" * 50)

        supabase_data = supabase_prep.get("supabase_data", {})
        start_time = time.time()
        upload_results = upload_to_supabase(supabase_data, supabase_client)
        upload_time = time.time() - start_time

        print(f"✅ Upload completed in {upload_time:.1f} seconds")

        # Step 5: Verify Data
        print(f"\n5️⃣ STEP 5: Verifying Data in Supabase")
        print("-" * 50)

        verification_results = verify_data_in_supabase(supabase_client, target_date)

        # Final Summary
        print(f"\n" + "=" * 70)
        print("📋 FULL DAG TEST SUMMARY")
        print("=" * 70)

        total_time = fetch_time + transform_time + prep_time + upload_time
        print(f"⏱️  Total execution time: {total_time:.1f} seconds")
        print(f"📊 API calls used: {analytics_data.get('api_calls_used', 0)}")
        print(f"📈 Total records processed: {supabase_prep.get('total_records', 0)}")

        # Upload success summary
        successful_uploads = sum(
            1 for result in upload_results.values() if result.get("success")
        )
        total_uploads = len(upload_results)
        print(f"📤 Upload success: {successful_uploads}/{total_uploads} tables")

        # Verification summary
        total_records_in_db = sum(
            result.get("record_count", 0) for result in verification_results.values()
        )
        print(f"🔍 Records verified in Supabase: {total_records_in_db}")

        if successful_uploads == total_uploads and total_records_in_db > 0:
            print("\n🎉 FULL DAG TEST PASSED! ✅")
            print("💡 The pipeline is ready for Airflow deployment")
        else:
            print("\n⚠️ FULL DAG TEST HAD ISSUES")
            print("🔧 Review the errors above before deploying")

        return {
            "success": successful_uploads == total_uploads,
            "execution_time": total_time,
            "records_processed": supabase_prep.get("total_records", 0),
            "records_in_db": total_records_in_db,
            "upload_results": upload_results,
            "verification_results": verification_results,
        }

    except Exception as e:
        print(f"\n❌ DAG TEST FAILED: {str(e)}")
        import traceback

        traceback.print_exc()
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    # Install required packages if missing
    try:
        import supabase
    except ImportError:
        print("📦 Installing required packages...")
        os.system("pip install supabase")
        import supabase

    # Run the test
    results = run_full_dag_test()

    # Exit with appropriate code
    exit(0 if results.get("success") else 1)
